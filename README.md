# AI ChatGPT Question-Answer Chatbox

This is a modern AI chatbox application built with [Next.js](https://nextjs.org) and TypeScript, using GitHub Models API to provide ChatGPT-powered responses.

## Features

- 🤖 **AI-Powered Chat**: Uses OpenAI's GPT-4o model through GitHub Models API
- 💬 **Real-time Messaging**: Instant responses with typing indicators
- 🎨 **Modern UI**: Clean, responsive design with Tailwind CSS
- 📱 **Mobile Friendly**: Works seamlessly on desktop and mobile devices
- 🔄 **Conversation History**: Maintains context throughout the conversation
- ⚡ **Fast Performance**: Built with Next.js for optimal performance

## Prerequisites

Before you begin, ensure you have:

1. **Node.js** (version 18 or higher)
2. **npm** or **yarn** package manager
3. **GitHub Personal Access Token** with access to GitHub Models

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
# Install dependencies
npm install
```

### 2. Configure GitHub Token

1. **Create a GitHub Personal Access Token (PAT)**:
   - Go to [GitHub Settings > Personal Access Tokens](https://github.com/settings/tokens)
   - Click "Generate new token (classic)"
   - Select the following scopes:
     - `repo` (if using private repositories)
     - `read:user`
   - Copy the generated token

2. **Set up environment variables**:
   ```bash
   # Copy the example environment file
   cp .env.example .env.local

   # Edit .env.local and add your GitHub token
   GITHUB_TOKEN=your_github_personal_access_token_here
   ```

### 3. Run the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the chatbox.

## Project Structure

```
src/
├── app/
│   ├── api/chat/route.ts      # API endpoint for chat requests
│   ├── layout.tsx             # Root layout
│   └── page.tsx               # Main chat page
├── components/chat/
│   ├── ChatContainer.tsx      # Main chat interface
│   ├── ChatInput.tsx          # Message input component
│   └── MessageBubble.tsx      # Individual message display
└── types/
    └── chat.ts                # TypeScript type definitions
```

## Usage

1. **Start a Conversation**: Type your message in the input field at the bottom
2. **Send Messages**: Press Enter or click the Send button
3. **View Responses**: AI responses appear in real-time with typing indicators
4. **Clear Chat**: Use the "Clear Chat" button to start a new conversation
5. **Conversation Context**: The AI remembers previous messages in the conversation

## API Endpoints

### POST `/api/chat`

Sends a message to the AI and returns a response.

**Request Body:**
```json
{
  "message": "Your question here",
  "messages": [
    {"role": "user", "content": "Previous message"},
    {"role": "assistant", "content": "Previous response"}
  ]
}
```

**Response:**
```json
{
  "message": "AI response",
  "success": true
}
```

## Troubleshooting

### Common Issues

1. **"GitHub token not configured" error**:
   - Ensure you've created a `.env.local` file
   - Verify your GitHub token is correctly set
   - Restart the development server after adding the token

2. **"Invalid GitHub token" error**:
   - Check that your GitHub token has the correct permissions
   - Ensure the token hasn't expired
   - Try generating a new token

3. **Rate limit exceeded**:
   - GitHub Models API has usage limits
   - Wait a few minutes before trying again
   - Consider implementing request throttling for production use

## Technologies Used

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **OpenAI SDK** - For GitHub Models API integration
- **React Hooks** - For state management

## Learn More

To learn more about the technologies used:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API
- [GitHub Models Documentation](https://docs.github.com/en/github-models) - learn about GitHub Models API
- [OpenAI API Documentation](https://platform.openai.com/docs) - understand the OpenAI API
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - learn about utility-first CSS

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

**Important**: Remember to add your `GITHUB_TOKEN` environment variable in your Vercel project settings.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
