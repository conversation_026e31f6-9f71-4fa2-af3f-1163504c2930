import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const token = process.env["GITHUB_TOKEN"];

export async function POST(request: NextRequest) {
  try {
    const { message, messages = [] } = await request.json();

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    if (!process.env.GITHUB_TOKEN) {
      return NextResponse.json(
        { error: 'GitHub token not configured. Please set GITHUB_TOKEN environment variable.' },
        { status: 500 }
      );
    }

    // Prepare the conversation history
    const conversationMessages = [
      { role: "system", content: "You are a helpful AI assistant. Provide clear, concise, and helpful responses." },
      ...messages,
      { role: "user", content: message }
    ];

    const client = new OpenAI({
    baseURL: "https://models.github.ai/inference",
    apiKey: token
  });

    // Make request to GitHub Models API
    const response = await client.chat.completions.create({
      messages: conversationMessages,
      model: "openai/gpt-4o",
      temperature: 0.7,
      max_tokens: 4096,
      top_p: 1
    });

    const assistantMessage = response.choices[0]?.message?.content;

    if (!assistantMessage) {
      return NextResponse.json(
        { error: 'No response from AI model' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: assistantMessage,
      success: true
    });

  } catch (error: unknown) {
    console.error('Chat API Error:', error);
    
    // Handle specific error types
    if (error && typeof error === 'object' && 'status' in error) {
      if (error.status === 401) {
        return NextResponse.json(
          { error: 'Invalid GitHub token. Please check your GITHUB_TOKEN environment variable.' },
          { status: 401 }
        );
      }

      if (error.status === 429) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to get response from AI model. Please try again.' },
      { status: 500 }
    );
  }
}
